#!/usr/bin/env python3
"""
Test script to verify that the pricing centralization is working correctly.
"""

import sys
import os

# Add the backend source to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'rayuela_backend', 'src'))

def test_pricing_centralization():
    """Test that pricing is correctly centralized and accessible."""

    try:
        # Import the necessary modules
        from db.enums import SubscriptionPlan, PLAN_LIMITS

        # Mock settings to avoid dependency issues
        class MockSettings:
            MERCADOPAGO_PRICE_STARTER_AMOUNT = 2500.0
            MERCADOPAGO_PRICE_PRO_AMOUNT = 5000.0
            MERCADOPAGO_PRICE_ENTERPRISE_AMOUNT = 15000.0

            @property
            def MERCADOPAGO_PRICE_AMOUNTS(self):
                return {
                    "STARTER": self.MERCADOPAGO_PRICE_STARTER_AMOUNT,
                    "PRO": self.MERCADOPAGO_PRICE_PRO_AMOUNT,
                    "ENTERPRISE": self.MERCADOPAGO_PRICE_ENTERPRISE_AMOUNT
                }

        settings = MockSettings()
        
        print("✅ Successfully imported modules")
        
        # Test 1: Check that PLAN_LIMITS contains price_ars for all plans
        print("\n🔍 Testing PLAN_LIMITS pricing...")
        for plan in SubscriptionPlan:
            plan_config = PLAN_LIMITS.get(plan, {})
            price_ars = plan_config.get("price_ars")
            print(f"  {plan.name}: ${price_ars} ARS")
            
            if price_ars is None:
                print(f"  ❌ Missing price_ars for {plan.name}")
                return False
        
        print("✅ All plans have price_ars defined in PLAN_LIMITS")
        
        # Test 2: Check that settings has the new price amount properties
        print("\n🔍 Testing settings price amounts...")
        try:
            price_amounts = settings.MERCADOPAGO_PRICE_AMOUNTS
            print(f"  MERCADOPAGO_PRICE_AMOUNTS: {price_amounts}")
            
            expected_keys = ["STARTER", "PRO", "ENTERPRISE"]
            for key in expected_keys:
                if key not in price_amounts:
                    print(f"  ❌ Missing {key} in MERCADOPAGO_PRICE_AMOUNTS")
                    return False
                print(f"  {key}: ${price_amounts[key]} ARS")
        
        except Exception as e:
            print(f"  ❌ Error accessing MERCADOPAGO_PRICE_AMOUNTS: {e}")
            return False
        
        print("✅ Settings price amounts are accessible")
        
        # Test 3: Verify consistency between PLAN_LIMITS and settings defaults
        print("\n🔍 Testing consistency between PLAN_LIMITS and settings...")
        
        plan_mapping = {
            SubscriptionPlan.STARTER: "STARTER",
            SubscriptionPlan.PRO: "PRO", 
            SubscriptionPlan.ENTERPRISE: "ENTERPRISE"
        }
        
        for plan, settings_key in plan_mapping.items():
            plan_limits_price = PLAN_LIMITS[plan]["price_ars"]
            settings_price = price_amounts[settings_key]
            
            if plan_limits_price != settings_price:
                print(f"  ❌ Price mismatch for {plan.name}: PLAN_LIMITS={plan_limits_price}, settings={settings_price}")
                return False
            else:
                print(f"  ✅ {plan.name}: ${plan_limits_price} ARS (consistent)")
        
        print("✅ Prices are consistent between PLAN_LIMITS and settings")
        
        print("\n🎉 All pricing centralization tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_pricing_centralization()
    sys.exit(0 if success else 1)
